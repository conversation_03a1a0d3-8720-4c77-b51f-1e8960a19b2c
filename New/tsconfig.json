{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@database": ["./src/database"], "@database/*": ["./src/database/*"], "@config": ["./src/utils/configs"], "@utils": ["./src/utils"], "@utils/*": ["./src/utils/*"], "@type": ["./src/type"], "@type/*": ["./src/type/*"], "@ccProcessor": ["./src/processors/cc"], "@ccProcessor/*": ["./src/processors/cc/*"], "@apProcessor": ["./src/processors/ap"], "@apProcessor/*": ["./src/processors/ap/*"], "@processor": ["./src/processors"], "@processor/*": ["./src/processors/*"], "@apiClient": ["./src/apiClient"], "@apiClient/*": ["./src/apiClient/*"], "@handlers": ["./src/handlers"], "@handlers/*": ["./src/handlers/*"]}}, "exclude": ["old"]}