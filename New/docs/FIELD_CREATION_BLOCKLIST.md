# Field Creation Blocklist System

## Overview

The Field Creation Blocklist system prevents specific AutoPatient (AP) custom fields from being created as CliniCore (CC) custom fields during the custom field synchronization process. This system provides fine-grained control over which fields should be replicated between platforms.

## Features

### 1. Configurable Blocklist
- **Location**: `New/src/config/standardFieldMappings.ts`
- **Constant**: `AP_TO_CC_CREATION_BLOCKLIST`
- **Type**: `BlocklistEntry[]`

### 2. Pattern Matching Support
- **Exact Matching**: Precise field name blocking
- **Regex Patterns**: Pattern-based blocking for field categories
- **Case Sensitivity**: Configurable case-sensitive/insensitive matching

### 3. Integration Points
- **Phase 4 Validation**: Integrated into AP → CC creation logic
- **Statistics Tracking**: New `creationBlockedCount` metric
- **Response Interface**: `blockedApFields` array in sync response

## Configuration

### BlocklistEntry Interface

```typescript
export interface BlocklistEntry {
  /** Field name pattern to block (exact match or regex pattern) */
  pattern: string;
  /** Whether this is a regex pattern (true) or exact match (false) */
  isRegex: boolean;
  /** Case-sensitive matching (default: false) */
  caseSensitive?: boolean;
  /** Reason why this field is blocked */
  reason: string;
}
```

### Default Blocklist Categories

1. **Internal System Fields**
   - IDs, timestamps, metadata
   - Pattern: `^(id|_id|internal_id)$`

2. **AP-Specific Workflow Fields**
   - Pipeline, funnel, lead scoring
   - Exact matches: `pipeline`, `funnel`, `lead_score`

3. **Communication Preferences**
   - DND, opt-out settings
   - Pattern: `^(dnd|do_not_disturb|email_opt_out|sms_opt_out)$`

4. **Legacy/Deprecated Fields**
   - Pattern: `^(legacy_|deprecated_|old_)`

5. **Test/Development Fields**
   - Pattern: `^(test_|dev_|debug_|temp_)`

6. **Platform-Specific Integrations**
   - Pattern: `^(zapier_|webhook_|api_key|integration_)`

## API Functions

### checkApToCcCreationBlocklist(fieldName: string)
```typescript
/**
 * Check if an AutoPatient field name is blocked from CC creation
 * @param fieldName - AP field name to check against blocklist
 * @returns Blocklist entry if field is blocked, null if allowed
 */
```

### getBlockedApFields(apFields: { name: string }[])
```typescript
/**
 * Get all blocked field names from a list of AP fields
 * @param apFields - Array of AP fields to check
 * @returns Array of objects containing the field and the blocklist entry
 */
```

## Integration Flow

### Phase 4: AP → CC Field Creation

1. **Standard Field Check**: Verify if field maps to CC standard field
2. **Blocklist Check**: ✨ **NEW** - Check if field is blocked from creation
3. **Existing Field Check**: Check for existing CC custom field conflicts
4. **Field Creation**: Create CC field if all checks pass

### Blocklist Validation Logic

```typescript
// Check if this field is blocked from CC creation
const blocklistEntry = checkApToCcCreationBlocklist(apField.name);
if (blocklistEntry) {
  // Field is blocked, add to blocked list and skip creation
  response.blockedApFields.push(apField);
  response.creationStatistics.creationBlockedCount++;

  logInfo("Blocked AP field from CC creation", {
    requestId,
    apFieldId: apField.id,
    apFieldName: apField.name,
    blocklistPattern: blocklistEntry.pattern,
    blocklistReason: blocklistEntry.reason,
    isRegex: blocklistEntry.isRegex,
  });
  continue;
}
```

## Response Interface Updates

### CustomFieldSyncResponse

**New Fields:**
```typescript
/** Array of AP fields that were blocked from CC creation */
blockedApFields: APGetCustomFieldType[];

/** Field creation statistics */
creationStatistics: {
  // ... existing fields
  creationBlockedCount: number; // NEW
};
```

## Logging and Monitoring

### Blocked Field Logging
- **Level**: INFO
- **Context**: Request ID, field details, blocklist reason
- **Pattern**: Includes matched pattern and regex flag

### Final Statistics
- **Blocked Count**: `creationBlockedCount` in creation statistics
- **Blocked Fields**: `blockedApFieldsCount` in final summary
- **Summary**: Updated to mention blocklist filtering

## Usage Examples

### Adding New Blocklist Entries

```typescript
// Add to AP_TO_CC_CREATION_BLOCKLIST array
{
  pattern: "marketing_score",
  isRegex: false,
  caseSensitive: false,
  reason: "Marketing scores are AP-specific and don't apply to clinical records",
},
{
  pattern: "^(crm_|sales_)",
  isRegex: true,
  caseSensitive: false,
  reason: "CRM and sales fields are not relevant for clinical systems",
}
```

### Testing Blocklist Rules

```typescript
import { checkApToCcCreationBlocklist } from "@/config/standardFieldMappings";

// Test specific field
const result = checkApToCcCreationBlocklist("pipeline");
if (result) {
  console.log(`Blocked: ${result.reason}`);
}
```

## Benefits

1. **Prevents Data Pollution**: Stops irrelevant AP fields from cluttering CC
2. **Maintains System Integrity**: Prevents conflicts with CC-specific workflows
3. **Configurable Control**: Easy to add/remove blocked patterns
4. **Comprehensive Tracking**: Full visibility into blocked operations
5. **Performance Optimization**: Avoids unnecessary API calls for blocked fields

## Backward Compatibility

- **Existing Functionality**: All existing sync behavior preserved
- **Additive Implementation**: No breaking changes to existing APIs
- **Optional Blocking**: Fields are only blocked if explicitly configured
- **Graceful Handling**: Blocked fields are tracked, not treated as errors
