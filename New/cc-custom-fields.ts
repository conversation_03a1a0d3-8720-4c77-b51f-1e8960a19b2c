const req = fetch("https://ccdemo.clinicore.eu/api/v1/customFields", {
  headers: {
    accept: "*/*",
    "accept-language": "de-at",
    authorization:
      "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  },
  body: null,
  method: "GET",
});

const customFields = [
    {
      id: 1,
      name: "gender",
      label: "Geschlecht",
      validation: "{}",
      type: "select",
      color: null,
      positions: [
        { id: 1, order: 1, tab: 1, customField: 1 },
        { id: 71, order: 5, tab: 5, customField: 1 },
      ],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 1,
          value: "m\u00e4nnlich",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 2,
          value: "weiblich",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 3,
          value: "andere",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [],
    },
    {
      id: 2,
      name: "title",
      label: "Titel",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 3,
      name: "title-suffix",
      label: "Titel (nachgestellt)",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 4,
      name: "health-insurance",
      label: "Krankenversicherung",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 4,
          value: "\u00d6GK",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 5,
          value: "SVS-GW",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 6,
          value: "SVS-LW",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 7,
          value: "BVAEB-OEB",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 8,
          value: "BVAEB-EB",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 9,
          value: "\u00d6GK-W",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 10,
          value: "\u00d6GK-N",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 11,
          value: "\u00d6GK-O",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 12,
          value: "\u00d6GK-ST",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 13,
          value: "\u00d6GK-S",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 14,
          value: "\u00d6GK-V",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 15,
          value: "\u00d6GK-K",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 16,
          value: "\u00d6GK-T",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 17,
          value: "\u00d6GK-B",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 18,
          value: "KFA Wien",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 19,
          value: "PVA",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 20,
          value: "Andere",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [],
    },
    {
      id: 5,
      name: "private-insurance",
      label: "Privatversicherung",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 6,
      name: "additionally-insured",
      label: "Zusatzversichert",
      validation: "{}",
      type: "boolean",
      color: "#ffffff",
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 7,
      name: "email",
      label: "E-Mail Adresse",
      validation: "{}",
      type: "email",
      color: null,
      positions: [
        { id: 7, order: 2, tab: 1, customField: 7 },
        { id: 37, order: 7, tab: 5, customField: 7 },
      ],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 8,
      name: "phone-mobile",
      label: "Telefon Mobil",
      validation: "{}",
      type: "telephone",
      color: null,
      positions: [
        { id: 8, order: 3, tab: 1, customField: 8 },
        { id: 38, order: 8, tab: 5, customField: 8 },
      ],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 9,
      name: "phone-personal",
      label: "Telefon Privat",
      validation: "{}",
      type: "telephone",
      color: null,
      positions: [],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 10,
      name: "phone-business",
      label: "Telefon Gesch\u00e4ftlich",
      validation: "{}",
      type: "telephone",
      color: null,
      positions: [],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 11,
      name: "reminder-delivery-methods",
      label: "Terminerinnerungen",
      validation: "{}",
      type: "select",
      color: null,
      positions: [
        { id: 41, order: 11, tab: 5, customField: 11 },
        { id: 75, order: 13, tab: 1, customField: 11 },
        { id: 76, order: 4, tab: 3, customField: 11 },
      ],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 21,
          value: "SMS",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 22,
          value: "E-Mail",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [
        {
          id: 21,
          value: "SMS",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
    },
    {
      id: 12,
      name: "newsletter-wanted",
      label: "Newsletter erw\u00fcnscht",
      validation: "{}",
      type: "boolean",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 13,
      name: "marital-status",
      label: "Familienstand",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 23,
          value: "ledig",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 24,
          value: "verheiratet",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 25,
          value: "verwitwet",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [],
    },
    {
      id: 14,
      name: "occupation",
      label: "Beruf",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 15,
      name: "note",
      label: "Notiz",
      validation: "{}",
      type: "textarea",
      color: null,
      positions: [
        { id: 15, order: 4, tab: 1, customField: 15 },
        { id: 45, order: 15, tab: 5, customField: 15 },
        { id: 53, order: 0, tab: 3, customField: 15 },
      ],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 16,
      name: "how-did-you-hear-about-us",
      label: "Zu uns gekommen durch",
      validation: "{}",
      type: "select",
      color: null,
      positions: [
        { id: 16, order: 5, tab: 1, customField: 16 },
        { id: 46, order: 16, tab: 5, customField: 16 },
      ],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 26,
          value: "Pers\u00f6nliche Empfehlung",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 27,
          value: "\u00dcberweisung",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 28,
          value: "Internetwerbung",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 29,
          value: "Gutschein",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 30,
          value: "Fernsehen",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 31,
          value: "Zeitschrift",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 32,
          value: "Adwords",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 33,
          value: "Facebook Ad",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 34,
          value: "Andere",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [],
    },
    {
      id: 17,
      name: "height",
      label: "Gr\u00f6\u00dfe (cm)",
      validation: '{"numericality":{"allowBlank":true}}',
      type: "number",
      color: null,
      positions: [{ id: 17, order: 1, tab: 2, customField: 17 }],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 18,
      name: "weight",
      label: "Gewicht (kg)",
      validation: '{"numericality":{"allowBlank":true}}',
      type: "number",
      color: null,
      positions: [{ id: 18, order: 2, tab: 2, customField: 18 }],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 19,
      name: "former-surgeries",
      label: "Fr\u00fchere Operationen",
      validation: "{}",
      type: "text",
      color: null,
      positions: [
        { id: 20, order: 3, tab: 2, customField: 19 },
        { id: 74, order: 1, tab: 3, customField: 19 },
      ],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 20,
      name: "allergies",
      label: "Allergien",
      validation: "{}",
      type: "text",
      color: "#dc4a4a",
      positions: [
        { id: 21, order: 4, tab: 2, customField: 20 },
        { id: 54, order: 2, tab: 3, customField: 20 },
      ],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 21,
      name: "medication",
      label: "Medikation",
      validation: "{}",
      type: "medication",
      color: null,
      positions: [{ id: 56, order: 3, tab: 3, customField: 21 }],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 22,
      name: "permanent-diagnoses",
      label: "Dauerdiagnosen",
      validation: "{}",
      type: "permanent-diagnoses",
      color: null,
      positions: [{ id: 22, order: 5, tab: 2, customField: 22 }],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 23,
      name: "chronic-diseases",
      label: "Chronische Erkrankungen",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 23, order: 6, tab: 2, customField: 23 }],
      allowMultipleValues: true,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 24,
      name: "co-insurance-name",
      label: "Mitversichert bei (Name)",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 25,
      name: "co-insurance-number",
      label: "Mitversichert bei (Nummer)",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 26,
      name: "augenfarbe",
      label: "Augenfarbe",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 1032,
          value: "Blau",
          createdAt: "2022-06-27T11:53:21.000Z",
          updatedAt: "2022-06-27T11:53:22.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 1033,
          value: "Gr\u00fcn",
          createdAt: "2022-06-27T11:53:21.000Z",
          updatedAt: "2022-06-27T11:53:22.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 27,
      name: "haarfarbe",
      label: "Haarfarbe",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 1034,
          value: "blond",
          createdAt: "2022-06-27T12:04:37.000Z",
          updatedAt: "2022-06-27T12:04:38.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 1035,
          value: "braun",
          createdAt: "2022-06-27T12:04:37.000Z",
          updatedAt: "2022-06-27T12:04:38.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 1036,
          value: "",
          createdAt: "2022-06-27T12:04:37.000Z",
          updatedAt: "2022-06-27T12:04:38.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 28,
      name: "blutdruck",
      label: "Blutdruck",
      validation: "{}",
      type: "number",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 29,
      name: "adresse",
      label: "Adresse",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 68, order: 9, tab: 1, customField: 29 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 30,
      name: "hausarzt",
      label: "Hausarzt",
      validation: "{}",
      type: "text",
      color: null,
      positions: [
        { id: 57, order: 0, tab: 1, customField: 30 },
        { id: 59, order: 6, tab: 2, customField: 30 },
      ],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 31,
      name: "hausarzt-stammfeld",
      label: "Hausarzt Stammfeld",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 58, order: 6, tab: 1, customField: 31 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 32,
      name: "befunde",
      label: "Befunde",
      validation: "{}",
      type: "boolean",
      color: "#f77070",
      positions: [{ id: 60, order: 7, tab: 2, customField: 32 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: true,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 33,
      name: "empfohlen-von",
      label: "Empfohlen von",
      validation: "{}",
      type: "patient-has-recommended",
      color: "#3c69b6",
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 34,
      name: "test-2---multiple-selection",
      label: "TEST 2 - multiple selection",
      validation: "{}",
      type: "select",
      color: null,
      positions: [{ id: 61, order: 8, tab: 2, customField: 34 }],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 3310,
          value: "1",
          createdAt: "2023-12-12T14:18:23.000Z",
          updatedAt: "2023-12-12T14:18:23.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3311,
          value: "2",
          createdAt: "2023-12-12T14:18:23.000Z",
          updatedAt: "2023-12-12T14:18:23.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3312,
          value: "3",
          createdAt: "2023-12-12T14:18:23.000Z",
          updatedAt: "2023-12-12T14:18:23.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3313,
          value: "4",
          createdAt: "2023-12-12T14:18:23.000Z",
          updatedAt: "2023-12-12T14:18:23.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 35,
      name: "allergie",
      label: "Allergie",
      validation: "{}",
      type: "select-or-custom",
      color: null,
      positions: [{ id: 62, order: 9, tab: 2, customField: 35 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 3319,
          value: "Pollen",
          createdAt: "2023-12-12T15:12:03.000Z",
          updatedAt: "2023-12-12T15:12:04.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3320,
          value: "Millben",
          createdAt: "2023-12-12T15:12:03.000Z",
          updatedAt: "2023-12-12T15:12:04.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3321,
          value: "Gras",
          createdAt: "2023-12-12T15:12:03.000Z",
          updatedAt: "2023-12-12T15:12:04.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3322,
          value: "Andere",
          createdAt: "2023-12-12T15:12:03.000Z",
          updatedAt: "2023-12-12T15:12:04.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 36,
      name: "alex-test",
      label: "AlexTest",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 3323,
          value: "one",
          createdAt: "2023-12-12T16:29:55.000Z",
          updatedAt: "2023-12-12T16:29:55.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3324,
          value: "two",
          createdAt: "2023-12-12T16:29:55.000Z",
          updatedAt: "2023-12-12T16:29:55.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 37,
      name: "test3---dropdown-multiple",
      label: "Test3 - Dropdown Multiple",
      validation: "{}",
      type: "select",
      color: null,
      positions: [{ id: 63, order: 10, tab: 2, customField: 37 }],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 3325,
          value: "1 - One",
          createdAt: "2023-12-12T20:13:07.000Z",
          updatedAt: "2023-12-13T08:09:46.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3326,
          value: "2 - Two",
          createdAt: "2023-12-12T20:13:07.000Z",
          updatedAt: "2023-12-13T08:09:46.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3327,
          value: "3 - Three",
          createdAt: "2023-12-12T20:13:07.000Z",
          updatedAt: "2023-12-13T08:09:46.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 3328,
          value: "4 - Four",
          createdAt: "2023-12-12T20:13:07.000Z",
          updatedAt: "2023-12-13T08:09:46.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 38,
      name: "external-id",
      label: "ExternalId",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 64, order: 7, tab: 1, customField: 38 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 39,
      name: "multiple-values-test",
      label: "MultipleValuesTest",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 65, order: 8, tab: 1, customField: 39 }],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 40,
      name: "allergien",
      label: "Allergien",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [
        {
          id: 3582,
          value: "Keine",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3583,
          value: "Novaminsulfon (Metamizol)",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3584,
          value: "Wespe",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3585,
          value: "Sonstige",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3586,
          value: "Penicillin",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3587,
          value: "Ibuprofen (NSAR)",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3588,
          value: "Soja",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
        {
          id: 3589,
          value: "Latex",
          createdAt: "2024-01-31T18:23:33.000Z",
          updatedAt: "2024-01-31T18:23:34.000Z",
          createdBy: 5003,
          updatedBy: 5003,
        },
      ],
      defaultValues: [],
    },
    {
      id: 41,
      name: "other-id",
      label: "Other",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 42,
      name: "quno-external-id",
      label: "Quno External ID",
      validation: "{}",
      type: "text",
      color: null,
      positions: [{ id: 69, order: 99, tab: 4, customField: 42 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 43,
      name: "insurance-category",
      label: "Versichertenkategorie",
      validation: "{}",
      type: "select",
      color: null,
      positions: [{ id: 70, order: 10, tab: 1, customField: 43 }],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        {
          id: 4134,
          value: "erwerbst\u00e4tig",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4135,
          value: "arbeitslos",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4136,
          value: "selbstversichert",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4137,
          value: "Pensionist",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4138,
          value: "Kriegshinterbliebener",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4139,
          value: "OFG",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4140,
          value: "KOVG, HVG",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4141,
          value: "KOVG-D",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4142,
          value: "STVG",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4143,
          value: "VOG",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4144,
          value: "MKP - Nichtversicherte",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4145,
          value: "VU - Nichtversicherte",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
        {
          id: 4146,
          value: "Fremdstaaten (De-facto-Versicherte)",
          createdAt: null,
          updatedAt: null,
          createdBy: null,
          updatedBy: null,
        },
      ],
      defaultValues: [],
    },
    {
      id: 44,
      name: "quno-external-id-1",
      label: "quno-external-id-1",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 45,
      name: "quno-external-id-2",
      label: "quno-external-id-2",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 46,
      name: "quno-external-id-3",
      label: "Quno External Id 3",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 47,
      name: "source",
      label: "Source",
      validation: "{}",
      type: "textarea",
      color: null,
      positions: [],
      allowMultipleValues: true,
      useCustomSort: "0",
      isRequired: true,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 48,
      name: "patient-source",
      label: "Patient source",
      validation: "{}",
      type: "select",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: true,
      allowedValues: [
        {
          id: 5551,
          value: "Friends and family",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
        {
          id: 5552,
          value: "Online ad",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
        {
          id: 5553,
          value: "Online article",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
        {
          id: 5554,
          value: "Offline ad",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
        {
          id: 5555,
          value: "Doctor",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
        {
          id: 5556,
          value: "Other",
          createdAt: "2024-09-25T09:14:48.000Z",
          updatedAt: "2024-09-25T09:14:48.000Z",
          createdBy: 5016,
          updatedBy: 5016,
        },
      ],
      defaultValues: [],
    },
    {
      id: 49,
      name: "patient-intake-source-",
      label: "Patient intake source",
      validation: "{}",
      type: "select",
      color: null,
      positions: [{ id: 72, order: 11, tab: 1, customField: 49 }],
      allowMultipleValues: false,
      useCustomSort: "1",
      isRequired: false,
      allowedValues: [
        {
          id: 5572,
          value: "Recommendation",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5574,
          value: "- Friends and family",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5575,
          value: "- Colleague",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5576,
          value: "- Doctor",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5577,
          value: "- Other recommendation",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5578,
          value: "Online",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5579,
          value: "- Ads",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5580,
          value: "-- Search ad",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5581,
          value: "-- Website ad",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5582,
          value: "-- Other ad",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5583,
          value: "- Search",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5584,
          value: "-- Google",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5585,
          value: "-- Bing",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5586,
          value: "-- Duckduckgo",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5587,
          value: "-- Other search",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5588,
          value: "- Social",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5589,
          value: "-- Facebook",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5590,
          value: "-- Instagram",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5591,
          value: "-- Tiktok",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5592,
          value: "-- Linkedin",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5593,
          value: "-- Other social",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5594,
          value: "- Website",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5595,
          value: "-- News",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5596,
          value: "-- Professional",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5597,
          value: "-- Personal blog",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5598,
          value: "-- Other website",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5599,
          value: "Offline",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5600,
          value: "- Printed media",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5601,
          value: "-- Press",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5602,
          value: "-- Outdoor ad",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5603,
          value: "-- Indoor ad",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5604,
          value: "-- Other printed media",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5605,
          value: "- Broadcast",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5606,
          value: "-- TV",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5607,
          value: "-- Radio",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5608,
          value: "-- Other broadcast",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5609,
          value: "- Events",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5610,
          value: "-- In office",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5611,
          value: "-- Conference",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5612,
          value: "-- Online event",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5613,
          value: "-- Other event",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5614,
          value: "Other",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5615,
          value: "- Walk in",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
        {
          id: 5616,
          value: "- Not sure",
          createdAt: "2024-10-02T08:16:46.000Z",
          updatedAt: "2024-10-02T08:18:01.000Z",
          createdBy: 5001,
          updatedBy: 5001,
        },
      ],
      defaultValues: [],
    },
    {
      id: 50,
      name: "testfeld",
      label: "Testfeld",
      validation: "{}",
      type: "text",
      color: "#7bf283",
      positions: [{ id: 73, order: 12, tab: 1, customField: 50 }],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 51,
      name: "test-2025",
      label: "Test 2025",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: "0",
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 52,
      name: "phonemobile",
      label: "phoneMobile",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
    {
      id: 53,
      name: "phone",
      label: "phone",
      validation: "{}",
      type: "text",
      color: null,
      positions: [],
      allowMultipleValues: false,
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: [],
    },
  ]
