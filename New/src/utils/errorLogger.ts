/**
 * Error Logging Utility
 *
 * Provides centralized error logging functionality that stores error details
 * in the database for monitoring and debugging purposes. Only logs actual
 * errors, not successful operations or warnings.
 */

import { dbSchema, getDb } from "@database";
import { getRequestId } from "@/utils/getRequestId";

/**
 * Error context information for logging
 */
export interface ErrorContext {
	/** Additional context data to store with the error */
	data?: Record<string, unknown>;
	/** Error category/type for classification */
	type: string;
	/** Optional stack trace */
	stack?: string;
}

/**
 * Log an error to the database
 * Automatically retrieves request ID from context
 *
 * @param error - The error object or message to log
 * @param context - Additional context information
 * @returns Promise<void> - Completes logging or fails silently
 */
export async function logError(
	error: Error | string,
	context: ErrorContext,
): Promise<void> {
	const requestId = getRequestId();

	try {
		const db = getDb();

		const errorMessage = error instanceof Error ? error.message : String(error);
		const errorStack = error instanceof Error ? error.stack : context.stack;

		await db.insert(dbSchema.errorLogs).values({
			requestId,
			message: errorMessage,
			stack: errorStack || null,
			type: context.type,
			data: context.data || null,
		});

		console.log(`[${requestId}] Error logged to database: ${context.type}`);
	} catch (dbError) {
		// Fail silently to avoid cascading errors
		// Log to console as fallback
		console.error(`[${requestId}] Failed to log error to database:`, dbError);
		console.error(`[${requestId}] Original error:`, error);
	}
}

/**
 * Log webhook processing errors
 * Automatically retrieves request ID from context
 *
 * @param error - The error that occurred
 * @param webhookData - Webhook payload data for context
 * @returns Promise<void>
 */
export async function logWebhookError(
	error: Error | string,
	webhookData?: Record<string, unknown>,
): Promise<void> {
	await logError(error, {
		type: "webhook_processing",
		data: {
			webhook: webhookData,
			timestamp: new Date().toISOString(),
		},
	});
}

/**
 * Log database operation errors
 * Automatically retrieves request ID from context
 *
 * @param error - The database error that occurred
 * @param operation - Description of the database operation
 * @param entityData - Entity data involved in the operation
 * @returns Promise<void>
 */
export async function logDatabaseError(
	error: Error | string,
	operation: string,
	entityData?: Record<string, unknown>,
): Promise<void> {
	await logError(error, {
		type: "database_operation",
		data: {
			operation,
			entity: entityData,
			timestamp: new Date().toISOString(),
		},
	});
}

/**
 * Log API client errors (AutoPatient, CliniCore)
 * Automatically retrieves request ID from context
 *
 * @param error - The API error that occurred
 * @param apiName - Name of the API (e.g., "autopatient", "clinicore")
 * @param endpoint - API endpoint that failed
 * @param requestData - Request data sent to the API
 * @returns Promise<void>
 */
export async function logApiError(
	error: Error | string,
	apiName: string,
	endpoint: string,
	requestData?: Record<string, unknown>,
): Promise<void> {
	await logError(error, {
		type: "api_client",
		data: {
			api: apiName,
			endpoint,
			request: requestData,
			timestamp: new Date().toISOString(),
		},
	});
}

/**
 * Log validation errors
 * Automatically retrieves request ID from context
 *
 * @param error - The validation error that occurred
 * @param validationType - Type of validation that failed
 * @param invalidData - The data that failed validation
 * @returns Promise<void>
 */
export async function logValidationError(
	error: Error | string,
	validationType: string,
	invalidData?: Record<string, unknown>,
): Promise<void> {
	await logError(error, {
		type: "validation",
		data: {
			validationType,
			invalidData,
			timestamp: new Date().toISOString(),
		},
	});
}
