/**
 * Standard Field Mappings Configuration
 *
 * Defines the mapping between standard fields on one platform and custom fields
 * on another platform. This prevents the system from attempting to create
 * duplicate custom fields when a field should map to an existing standard field.
 *
 * **Key Concepts:**
 * - AP Standard Fields: Built-in contact fields in AutoPatient (email, phone, firstName, etc.)
 * - CC Standard Fields: Built-in patient fields in CliniCore (firstName, lastName, email, etc.)
 * - Cross-Platform Mappings: When a custom field on one platform corresponds to a standard field on another
 *
 * **Usage:**
 * - Prevents creation of AP custom field "phone" when CC custom field "phone" should map to AP standard field "phone"
 * - Prevents creation of CC custom field "source" when AP custom field "source" should map to CC standard field (if any)
 */

/**
 * AutoPatient standard contact fields
 * These are built-in fields that cannot be created as custom fields
 */
export const AP_STANDARD_FIELDS = new Set([
	// Core contact information
	"email",
	"phone",
	"firstName",
	"lastName",
	"name",
	"companyName",
	
	// Address fields
	"address1",
	"address",
	"city",
	"state",
	"country",
	"postalCode",
	"zipCode",
	
	// Contact metadata
	"source",
	"timezone",
	"dateOfBirth",
	"dob",
	"ssn",
	"gender",
	"assignedTo",
	"tags",
	"dnd",
	
	// System fields
	"id",
	"locationId",
	"emailLowerCase",
	"createdAt",
	"updatedAt",
]);

/**
 * CliniCore standard patient fields
 * These are built-in fields that cannot be created as custom fields
 */
export const CC_STANDARD_FIELDS = new Set([
	// Core patient information
	"firstName",
	"lastName",
	"email",
	"phoneMobile",
	"phone", // Often stored as custom field but maps to phoneMobile
	
	// Patient metadata
	"dob",
	"dateOfBirth",
	"ssn",
	"gender",
	"active",
	"title",
	"titleSuffix",
	"healthInsurance",
	
	// System fields
	"id",
	"createdAt",
	"updatedAt",
	"createdBy",
	"updatedBy",
	"flashMessage",
	"qrUrl",
	"avatarUrl",
]);

/**
 * Field mapping interface for cross-platform standard field mappings
 */
export interface StandardFieldMapping {
	/** Source field name (custom field name on source platform) */
	sourceField: string;
	/** Target field name (standard field name on target platform) */
	targetField: string;
	/** Source platform where this is a custom field */
	sourcePlatform: "ap" | "cc";
	/** Target platform where this is a standard field */
	targetPlatform: "ap" | "cc";
	/** Optional field transformation notes */
	notes?: string;
}

/**
 * Cross-platform standard field mappings
 * Maps custom fields on one platform to standard fields on another
 */
export const STANDARD_FIELD_MAPPINGS: StandardFieldMapping[] = [
	// CC custom fields → AP standard fields
	{
		sourceField: "phone",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phone' maps to AP standard field 'phone'",
	},
	{
		sourceField: "phone-mobile",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phone-mobile' maps to AP standard field 'phone'",
	},
	{
		sourceField: "phoneMobile",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phoneMobile' maps to AP standard field 'phone'",
	},
	{
		sourceField: "email",
		targetField: "email",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'email' maps to AP standard field 'email'",
	},
	{
		sourceField: "source",
		targetField: "source",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'source' maps to AP standard field 'source'",
	},
	{
		sourceField: "gender",
		targetField: "gender",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'gender' maps to AP standard field 'gender'",
	},
	{
		sourceField: "dateOfBirth",
		targetField: "dateOfBirth",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'dateOfBirth' maps to AP standard field 'dateOfBirth'",
	},
	{
		sourceField: "dob",
		targetField: "dateOfBirth",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'dob' maps to AP standard field 'dateOfBirth'",
	},
	
	// AP custom fields → CC standard fields
	{
		sourceField: "firstName",
		targetField: "firstName",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'firstName' maps to CC standard field 'firstName'",
	},
	{
		sourceField: "lastName",
		targetField: "lastName",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'lastName' maps to CC standard field 'lastName'",
	},
	{
		sourceField: "email",
		targetField: "email",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'email' maps to CC standard field 'email'",
	},
	{
		sourceField: "phone",
		targetField: "phoneMobile",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'phone' maps to CC standard field 'phoneMobile'",
	},
	{
		sourceField: "gender",
		targetField: "gender",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'gender' maps to CC standard field 'gender'",
	},
	{
		sourceField: "dateOfBirth",
		targetField: "dob",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'dateOfBirth' maps to CC standard field 'dob'",
	},
	{
		sourceField: "dob",
		targetField: "dob",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'dob' maps to CC standard field 'dob'",
	},
];

/**
 * Check if a field name conflicts with standard fields on the target platform
 *
 * @param fieldName - Field name to check
 * @param targetPlatform - Platform to check against ("ap" | "cc")
 * @returns True if the field name conflicts with a standard field
 */
export function isStandardFieldConflict(
	fieldName: string,
	targetPlatform: "ap" | "cc",
): boolean {
	const standardFields = targetPlatform === "ap" ? AP_STANDARD_FIELDS : CC_STANDARD_FIELDS;
	return standardFields.has(fieldName.toLowerCase());
}

/**
 * Find standard field mapping for a given field
 *
 * @param fieldName - Source field name to find mapping for
 * @param sourcePlatform - Platform where this is a custom field
 * @param targetPlatform - Platform where this should map to a standard field
 * @returns Standard field mapping if found, null otherwise
 */
export function findStandardFieldMapping(
	fieldName: string,
	sourcePlatform: "ap" | "cc",
	targetPlatform: "ap" | "cc",
): StandardFieldMapping | null {
	return STANDARD_FIELD_MAPPINGS.find(
		mapping =>
			mapping.sourceField.toLowerCase() === fieldName.toLowerCase() &&
			mapping.sourcePlatform === sourcePlatform &&
			mapping.targetPlatform === targetPlatform,
	) || null;
}

/**
 * Get all standard field mappings for a specific platform direction
 *
 * @param sourcePlatform - Source platform
 * @param targetPlatform - Target platform
 * @returns Array of standard field mappings
 */
export function getStandardFieldMappings(
	sourcePlatform: "ap" | "cc",
	targetPlatform: "ap" | "cc",
): StandardFieldMapping[] {
	return STANDARD_FIELD_MAPPINGS.filter(
		mapping =>
			mapping.sourcePlatform === sourcePlatform &&
			mapping.targetPlatform === targetPlatform,
	);
}
