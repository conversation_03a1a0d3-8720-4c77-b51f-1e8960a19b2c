-- Add support for standard field mappings to custom_fields table
-- This migration adds fields to track when custom fields map to standard fields
-- on the target platform, preventing duplicate field creation attempts

-- Add mapping type column to distinguish between different mapping types
ALTER TABLE "custom_fields" 
ADD COLUMN "mapping_type" varchar(50) DEFAULT 'custom_to_custom';

-- Add standard field mapping columns
ALTER TABLE "custom_fields" 
ADD COLUMN "ap_standard_field" varchar(255);

ALTER TABLE "custom_fields" 
ADD COLUMN "cc_standard_field" varchar(255);

-- Add comments for documentation
COMMENT ON COLUMN "custom_fields"."mapping_type" IS 'Type of field mapping: custom_to_custom, custom_to_standard, standard_to_custom';
COMMENT ON COLUMN "custom_fields"."ap_standard_field" IS 'AP standard field name if this maps to an AP standard field';
COMMENT ON COLUMN "custom_fields"."cc_standard_field" IS 'CC standard field name if this maps to a CC standard field';

-- Create index for efficient lookups by mapping type
CREATE INDEX "idx_custom_fields_mapping_type" ON "custom_fields" ("mapping_type");

-- Create index for standard field lookups
CREATE INDEX "idx_custom_fields_ap_standard" ON "custom_fields" ("ap_standard_field") WHERE "ap_standard_field" IS NOT NULL;
CREATE INDEX "idx_custom_fields_cc_standard" ON "custom_fields" ("cc_standard_field") WHERE "cc_standard_field" IS NOT NULL;
