CREATE TABLE "custom_fields" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255),
	"cc_id" integer,
	"name" varchar(255),
	"label" varchar(255),
	"type" varchar(255),
	"ap_config" jsonb,
	"cc_config" jsonb,
	CONSTRAINT "custom_fields_ap_id_unique" UNIQUE("ap_id"),
	CONSTRAINT "custom_fields_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
DROP TABLE "ap_custom_fields" CASCADE;--> statement-breakpoint
DROP TABLE "cc_custom_fields" CASCADE;