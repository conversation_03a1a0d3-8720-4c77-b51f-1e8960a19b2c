/**
 * Custom Field Conversion Utilities
 *
 * Bidirectional custom field conversion utilities for AutoPatient and CliniCore platforms.
 * Provides intelligent field type mapping, reversible transformations, dynamic option
 * synchronization, and comprehensive logging for data synchronization operations.
 *
 * **Available Functions:**
 * - `apToCcCustomFieldConvert`: Convert AutoPatient fields to CliniCore format
 * - `ccToApCustomFieldConvert`: Convert CliniCore fields to AutoPatient format
 *
 * **Key Features:**
 * - Bidirectional field type conversion with data integrity preservation
 * - Enhanced boolean detection (any 2-option field → boolean type)
 * - Dynamic option synchronization with update detection
 * - Comprehensive option mapping for select/radio fields
 * - Graceful fallback handling for unmappable field types
 * - Automatic request ID integration with detailed logging
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for bulk conversion operations
 *
 * @example
 * ```typescript
 * import { apToCcCustomFieldConvert, ccToApCustomFieldConvert } from '@processors/customFields';
 *
 * // Convert AP field to CC format (automatic request ID)
 * const ccField = apToCcCustomFieldConvert(apField);
 *
 * // Convert CC field to AP format (automatic request ID)
 * const apField = ccToApCustomFieldConvert(ccField);
 *
 * // Bidirectional conversion preserves data integrity
 * const originalAp = apField;
 * const convertedCc = apToCcCustomFieldConvert(originalAp);
 * const backToAp = ccToApCustomFieldConvert(convertedCc);
 * // backToAp maintains compatibility with originalAp
 * ```
 *
 * @since 1.0.0
 * @version 2.0.0
 */

export { default as apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
export { default as ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";
