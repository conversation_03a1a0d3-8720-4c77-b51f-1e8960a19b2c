/**
 * AutoPatient to CliniCore Custom Field Conversion Utility
 *
 * Transforms AutoPatient custom field structures to CliniCore format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules:**
 * - AP RADIO (2 Yes/No options) → CC boolean
 * - AP RADIO (>2 options or non-Yes/No) → CC select
 * - AP MULTIPLE_OPTIONS → CC select (allowMultipleValues: true)
 * - AP SINGLE_OPTIONS → CC select (allowMultipleValues: false)
 * - AP TEXT → CC text
 * - Fallback: unmappable types → CC text
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type { APGetCustomFieldType, PostCCCustomField } from "@type";
import { logCustomField, logWarn } from "@/utils/logger";

/**
 * Convert AutoPatient custom field to CliniCore format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Automatically detects any 2-option RADIO field as a boolean candidate.
 *
 * @param apField - AutoPatient custom field object to convert
 * @returns CliniCore custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert AP radio field with any 2 options to CC boolean
 * const apRadioField: APGetCustomFieldType = {
 *   id: "field123",
 *   name: "Newsletter Subscription",
 *   dataType: "RADIO",
 *   picklistOptions: ["Accept", "Decline"]
 * };
 *
 * const ccField = apToCcCustomFieldConvert(apRadioField);
 * // Result: { name: "Newsletter Subscription", label: "Newsletter Subscription", type: "boolean", ... }
 *
 * // Convert AP multiple options to CC select
 * const apMultiField: APGetCustomFieldType = {
 *   id: "field456",
 *   name: "Interests",
 *   dataType: "MULTIPLE_OPTIONS",
 *   picklistOptions: ["Sports", "Music", "Travel"]
 * };
 *
 * const ccMultiField = apToCcCustomFieldConvert(apMultiField);
 * // Result: { type: "select", allowMultipleValues: true, allowedValues: [...], ... }
 * ```
 */
export function apToCcCustomFieldConvert(
	apField: APGetCustomFieldType,
): PostCCCustomField {
	logCustomField("AP→CC conversion started", apField.name, {
		apFieldType: apField.dataType,
		hasOptions: Boolean(apField.picklistOptions?.length),
		optionCount: apField.picklistOptions?.length || 0,
	});

	// Base field structure with common properties
	const baseField: PostCCCustomField = {
		name: apField.name,
		label: apField.name, // Use name as label by default
		type: "text", // Default fallback type
		validation: "{}",
		isRequired: false,
		allowMultipleValues: false,
	};

	// Handle AP RADIO field type
	if (apField.dataType === "RADIO") {
		return handleApRadioField(apField, baseField);
	}

	// Handle AP MULTIPLE_OPTIONS field type
	if (apField.dataType === "MULTIPLE_OPTIONS") {
		return handleApMultipleOptionsField(apField, baseField);
	}

	// Handle AP SINGLE_OPTIONS field type
	if (apField.dataType === "SINGLE_OPTIONS") {
		return handleApSingleOptionsField(apField, baseField);
	}

	// Handle AP TEXT field type
	if (apField.dataType === "TEXT") {
		logCustomField("AP→CC TEXT conversion", apField.name);
		return {
			...baseField,
			type: "text",
		};
	}

	// Fallback for unmappable field types
	logWarn(
		`AP→CC fallback conversion for unmappable type: ${apField.dataType}`,
		{
			fieldName: apField.name,
			originalType: apField.dataType,
			convertedType: "text",
		},
	);

	return {
		...baseField,
		type: "text",
	};
}

/**
 * Handle AP RADIO field conversion with intelligent 2-option detection
 *
 * Converts AP RADIO fields to either CC boolean (for any 2-option fields) or
 * CC select (for fields with more than 2 options). Provides reversible transformations
 * and handles dynamic option updates.
 *
 * @param apField - AP field with RADIO dataType
 * @param baseField - Base CC field structure
 * @returns Converted CC field (boolean or select type)
 */
function handleApRadioField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	// Check if this is a 2-option boolean field (any 2 options = boolean)
	if (isTwoOptionField(options)) {
		logCustomField("AP→CC RADIO→boolean conversion", apField.name, {
			detectedAsBoolean: true,
			originalOptions: options,
			conversionReason: "exactly_2_options",
		});

		return {
			...baseField,
			type: "boolean",
		};
	}

	// Convert to select field for multi-option radio fields
	logCustomField("AP→CC RADIO→select conversion", apField.name, {
		optionCount: options.length,
		options: options,
		conversionReason:
			options.length === 0 ? "no_options" : "more_than_2_options",
	});

	// Log potential dynamic option updates
	if (options.length > 10) {
		logWarn("Large option set detected - monitor for dynamic updates", {
			fieldName: apField.name,
			optionCount: options.length,
		});
	}

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP MULTIPLE_OPTIONS field conversion
 *
 * Converts AP MULTIPLE_OPTIONS to CC select with allowMultipleValues: true.
 * Preserves all option values for reversible transformation and handles
 * dynamic option updates.
 *
 * @param apField - AP field with MULTIPLE_OPTIONS dataType
 * @param baseField - Base CC field structure
 * @returns CC select field with multiple values enabled
 */
function handleApMultipleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC MULTIPLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: true,
		dynamicOptionsSupported: true,
	});

	// Log potential for dynamic option updates
	if (options.length === 0) {
		logWarn(
			"Empty options detected - field may support dynamic option addition",
			{
				fieldName: apField.name,
				fieldType: "MULTIPLE_OPTIONS",
			},
		);
	}

	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP SINGLE_OPTIONS field conversion
 *
 * Converts AP SINGLE_OPTIONS to CC select with allowMultipleValues: false.
 * Maintains single-selection behavior in the target system and handles
 * dynamic option updates.
 *
 * @param apField - AP field with SINGLE_OPTIONS dataType
 * @param baseField - Base CC field structure
 * @returns CC select field with single value selection
 */
function handleApSingleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC SINGLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: false,
		dynamicOptionsSupported: true,
	});

	// Check for potential boolean conversion opportunity
	if (options.length === 2) {
		logCustomField(
			"2-option SINGLE_OPTIONS detected - could be boolean candidate",
			apField.name,
			{
				options: options,
				suggestion: "consider_boolean_conversion",
			},
		);
	}

	// Log potential for dynamic option updates
	if (options.length === 0) {
		logWarn(
			"Empty options detected - field may support dynamic option addition",
			{
				fieldName: apField.name,
				fieldType: "SINGLE_OPTIONS",
			},
		);
	}

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Check if options array represents a 2-option field suitable for boolean conversion
 *
 * Detects any field with exactly 2 options as a boolean candidate, regardless of
 * the option text content. This broader approach supports various binary choices
 * beyond traditional Yes/No patterns and handles dynamic option updates.
 *
 * @param options - Array of option strings to analyze
 * @returns True if options represent a 2-option boolean choice
 *
 * @example
 * ```typescript
 * isTwoOptionField(["Yes", "No"]) // true
 * isTwoOptionField(["Accept", "Decline"]) // true
 * isTwoOptionField(["Enabled", "Disabled"]) // true
 * isTwoOptionField(["True", "False"]) // true
 * isTwoOptionField(["Option A", "Option B"]) // true
 * isTwoOptionField(["Yes", "No", "Maybe"]) // false
 * isTwoOptionField([]) // false
 * isTwoOptionField(["Single Option"]) // false
 * ```
 */
function isTwoOptionField(options: string[]): boolean {
	// Must have exactly 2 options for boolean conversion
	return options.length === 2;
}

/**
 * Detect and log potential dynamic option updates
 *
 * Analyzes option patterns to identify fields that may support dynamic
 * option addition, removal, or modification after initial creation.
 *
 * @param options - Current option array
 * @param fieldName - Field name for logging
 */
function detectDynamicOptionUpdates(
	options: string[],
	fieldName: string,
): void {
	// Check for patterns that suggest dynamic updates
	const hasNumberedOptions = options.some((opt) => /^\d+\./.test(opt));
	const hasVersionedOptions = options.some((opt) => /v\d+|version/i.test(opt));
	const hasDateOptions = options.some((opt) => /\d{4}|\d{2}\/\d{2}/.test(opt));

	if (hasNumberedOptions || hasVersionedOptions || hasDateOptions) {
		logCustomField("Dynamic option pattern detected", fieldName, {
			hasNumberedOptions,
			hasVersionedOptions,
			hasDateOptions,
			optionCount: options.length,
			recommendation: "monitor_for_option_updates",
		});
	}
}

export default apToCcCustomFieldConvert;
