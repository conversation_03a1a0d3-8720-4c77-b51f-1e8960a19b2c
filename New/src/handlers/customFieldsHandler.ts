/**
 * Custom Fields Synchronization Handler
 *
 * Provides comprehensive custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms with intelligent field matching, database upserts,
 * automatic field creation, and detailed tracking of matched/unmatched fields.
 *
 * Features:
 * - Fuzzy field matching with normalization
 * - Bidirectional field mapping storage
 * - Automatic field creation on target platforms
 * - Comprehensive statistics and error tracking
 * - TypeScript strict compliance
 * - Request ID tracing for debugging
 */

import apiClient from "@apiClient";
import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";
import type { Context } from "hono";
import { logError as logDbError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { matchString } from "@/utils/matchString";
import { apToCcCustomFieldConvert } from "@/processors/customFields/apToCcCustomFieldConvert";
import { ccToApCustomFieldConvert } from "@/processors/customFields/ccToApCustomFieldConvert";
import {
	checkApToCcCreationBlocklist,
	findStandardFieldMapping,
	isStandardFieldConflict,
	type StandardFieldMapping,
} from "@/config/standardFieldMappings";

/**
 * Custom field synchronization response interface
 */
export interface CustomFieldSyncResponse {
	/** Number of successfully matched field pairs */
	matchedCount: number;
	/** Number of successfully upserted database records */
	upsertedCount: number;
	/** Array of AP fields that couldn't be matched */
	unmatchedApFields: APGetCustomFieldType[];
	/** Array of CC fields that couldn't be matched */
	unmatchedCcFields: GetCCCustomField[];
	/** Array of AP fields that were successfully created in CC */
	createdCcFields: GetCCCustomField[];
	/** Array of CC fields that were successfully created in AP */
	createdApFields: APGetCustomFieldType[];
	/** Array of AP fields that were blocked from CC creation */
	blockedApFields: APGetCustomFieldType[];
	/** Array of standard field mappings found during synchronization */
	standardFieldMappings: StandardFieldMapping[];
	/** Comprehensive processing statistics */
	statistics: {
		totalApFields: number;
		totalCcFields: number;
		totalProcessed: number;
		totalMatched: number;
		totalUnmatched: number;
		totalStandardMappings: number;
	};
	/** Field creation statistics */
	creationStatistics: {
		apFieldsCreatedInCc: number;
		ccFieldsCreatedInAp: number;
		totalCreated: number;
		creationErrors: number;
		creationSkippedDueToStandardFields: number;
		creationBlockedCount: number;
	};
	/** Array of errors encountered during processing */
	errors: string[];
	/** Array of errors encountered during field creation */
	creationErrors: string[];
}

/**
 * Database record type for custom field mappings
 */
type CustomFieldInsert = typeof dbSchema.customFields.$inferInsert;

/**
 * Determine if two custom fields match using comprehensive comparison
 *
 * Enhanced matching logic that compares fields by name, label, and fieldKey properties
 * using normalized string matching. Handles case differences, special characters, umlauts,
 * and provides multiple matching strategies for better field detection.
 *
 * @param apField - AutoPatient custom field object
 * @param ccField - CliniCore custom field object
 * @returns True if fields are considered a match, false otherwise
 */
function fieldsMatch(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	// Strategy 1: Exact name matching (case-insensitive)
	if (apField.name.toLowerCase() === ccField.name.toLowerCase()) {
		return true;
	}

	// Strategy 2: AP name with CC label (case-insensitive)
	if (apField.name.toLowerCase() === ccField.label.toLowerCase()) {
		return true;
	}

	// Strategy 3: Fuzzy matching using existing matchString utility
	if (
		matchString(apField.name, ccField.name) ||
		matchString(apField.name, ccField.label)
	) {
		return true;
	}

	// Strategy 4: AP fieldKey matching (if fieldKey exists)
	if (apField.fieldKey) {
		// Extract field name from fieldKey (e.g., "contact.allergien" -> "allergien")
		const fieldKeyName = apField.fieldKey.split('.').pop() || apField.fieldKey;

		if (
			fieldKeyName.toLowerCase() === ccField.name.toLowerCase() ||
			fieldKeyName.toLowerCase() === ccField.label.toLowerCase() ||
			matchString(fieldKeyName, ccField.name) ||
			matchString(fieldKeyName, ccField.label)
		) {
			return true;
		}
	}

	return false;
}

/**
 * Find existing custom field by name in a list of fields
 *
 * Performs case-insensitive and fuzzy matching to find existing custom fields
 * that match the given name. This prevents duplicate field creation.
 *
 * @param fieldName - Name to search for
 * @param existingFields - Array of existing custom fields to search in
 * @param platform - Platform type for logging context
 * @returns Matching field if found, null otherwise
 */
function findExistingCustomField(
	fieldName: string,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	platform: "ap" | "cc",
): APGetCustomFieldType | GetCCCustomField | null {
	for (const field of existingFields) {
		// Strategy 1: Exact name match (case-insensitive)
		if (field.name.toLowerCase() === fieldName.toLowerCase()) {
			return field;
		}

		// Strategy 2: For CC fields, check label as well
		if (platform === "cc" && "label" in field) {
			const ccField = field as GetCCCustomField;
			if (ccField.label.toLowerCase() === fieldName.toLowerCase()) {
				return field;
			}
		}

		// Strategy 3: For AP fields, check fieldKey
		if (platform === "ap" && "fieldKey" in field) {
			const apField = field as APGetCustomFieldType;
			if (apField.fieldKey) {
				const fieldKeyName = apField.fieldKey.split('.').pop() || apField.fieldKey;
				if (fieldKeyName.toLowerCase() === fieldName.toLowerCase()) {
					return field;
				}
			}
		}

		// Strategy 4: Fuzzy matching as fallback
		if (matchString(fieldName, field.name)) {
			return field;
		}

		// Strategy 5: For CC fields, fuzzy match against label
		if (platform === "cc" && "label" in field) {
			const ccField = field as GetCCCustomField;
			if (matchString(fieldName, ccField.label)) {
				return field;
			}
		}
	}

	return null;
}

/**
 * Create an AP field in CC platform using field conversion
 *
 * @param apField - AP field to convert and create in CC
 * @param requestId - Request ID for tracing
 * @returns Created CC field or null if creation failed
 */
async function createApFieldInCc(
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<GetCCCustomField | null> {
	try {
		logDebug("Converting AP field to CC format", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldType: apField.dataType,
		});

		const ccFieldData = apToCcCustomFieldConvert(apField);
		const createdField = await apiClient.cc.ccCustomfieldReq.create(ccFieldData);

		logInfo("Successfully created CC field from AP field", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			ccFieldId: createdField.id,
			ccFieldName: createdField.name,
		});

		return createdField;
	} catch (error) {
		// Check if error indicates field already exists
		const errorMessage = error instanceof Error ? error.message : String(error);
		if (errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict")) {

			logInfo("CC field creation failed due to existing field", {
				requestId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				errorMessage,
				action: "field_already_exists",
			});
		} else {
			logError(`Failed to create CC field from AP field ${apField.id}`, error);
		}
		return null;
	}
}

/**
 * Create a CC field in AP platform using field conversion
 *
 * @param ccField - CC field to convert and create in AP
 * @param requestId - Request ID for tracing
 * @returns Created AP field or null if creation failed
 */
async function createCcFieldInAp(
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logDebug("Converting CC field to AP format", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
		});

		const apFieldData = ccToApCustomFieldConvert(ccField);
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		logInfo("Successfully created AP field from CC field", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			apFieldId: createdField.id,
			apFieldName: createdField.name,
		});

		return createdField;
	} catch (error) {
		// Check if error indicates field already exists
		const errorMessage = error instanceof Error ? error.message : String(error);
		if (errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken")) {

			logInfo("AP field creation failed due to existing field", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				errorMessage,
				action: "field_already_exists",
			});
		} else {
			logError(`Failed to create AP field from CC field ${ccField.id}`, error);
		}
		return null;
	}
}

/**
 * Store database mapping for newly created field pair
 *
 * @param apField - AP field in the mapping
 * @param ccField - CC field in the mapping
 * @param requestId - Request ID for tracing
 */
async function storeMappingForCreatedFields(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		const mappingData: CustomFieldInsert = {
			apId: apField.id,
			ccId: ccField.id,
			name: apField.name,
			label: ccField.label,
			type: ccField.type,
			apConfig: apField,
			ccConfig: ccField,
			mappingType: "custom_to_custom",
			apStandardField: null,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.apId],
				set: {
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored mapping for newly created field pair", {
			requestId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
	} catch (error) {
		logError(
			`Failed to store mapping for created fields: AP ${apField.id} -> CC ${ccField.id}`,
			error,
		);
		throw error;
	}
}

/**
 * Store database mapping for standard field mapping
 *
 * @param customField - Custom field (AP or CC)
 * @param standardFieldMapping - Standard field mapping information
 * @param requestId - Request ID for tracing
 */
async function storeStandardFieldMapping(
	customField: APGetCustomFieldType | GetCCCustomField,
	standardFieldMapping: StandardFieldMapping,
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		const isApCustomField = "dataType" in customField;

		const mappingData: CustomFieldInsert = {
			apId: isApCustomField ? customField.id : null,
			ccId: !isApCustomField ? customField.id : null,
			name: customField.name,
			label: isApCustomField ? customField.name : (customField as GetCCCustomField).label,
			type: isApCustomField ? customField.dataType : (customField as GetCCCustomField).type,
			apConfig: isApCustomField ? customField : null,
			ccConfig: !isApCustomField ? customField : null,
			mappingType: standardFieldMapping.sourcePlatform === "ap" ? "custom_to_standard" : "standard_to_custom",
			apStandardField: standardFieldMapping.targetPlatform === "ap" ? standardFieldMapping.targetField : null,
			ccStandardField: standardFieldMapping.targetPlatform === "cc" ? standardFieldMapping.targetField : null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: isApCustomField ? [dbSchema.customFields.apId] : [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored standard field mapping", {
			requestId,
			customFieldId: customField.id,
			customFieldName: customField.name,
			standardField: standardFieldMapping.targetField,
			mappingType: mappingData.mappingType,
		});
	} catch (error) {
		logError(
			`Failed to store standard field mapping for ${customField.name}`,
			error,
		);
		throw error;
	}
}

/**
 * Check if a field should be skipped due to standard field conflict
 *
 * @param fieldName - Field name to check
 * @param sourcePlatform - Platform where this is a custom field
 * @param targetPlatform - Platform where we want to create the field
 * @param requestId - Request ID for tracing
 * @returns Standard field mapping if found, null if field can be created
 */
function checkForStandardFieldMapping(
	fieldName: string,
	sourcePlatform: "ap" | "cc",
	targetPlatform: "ap" | "cc",
	requestId: string,
): StandardFieldMapping | null {
	// Check if this field maps to a standard field on the target platform
	const standardMapping = findStandardFieldMapping(fieldName, sourcePlatform, targetPlatform);

	if (standardMapping) {
		logInfo("Found standard field mapping", {
			requestId,
			sourceField: fieldName,
			sourcePlatform,
			targetField: standardMapping.targetField,
			targetPlatform,
			notes: standardMapping.notes,
		});
		return standardMapping;
	}

	// Check if the field name conflicts with a standard field on the target platform
	if (isStandardFieldConflict(fieldName, targetPlatform)) {
		logInfo("Field name conflicts with standard field", {
			requestId,
			fieldName,
			targetPlatform,
			action: "skipping_creation",
		});

		// Create a mapping for this conflict
		return {
			sourceField: fieldName,
			targetField: fieldName,
			sourcePlatform,
			targetPlatform,
			notes: `Field name conflicts with ${targetPlatform.toUpperCase()} standard field`,
		};
	}

	return null;
}

/**
 * Check if a field conflicts with existing custom fields on the target platform
 *
 * This function prevents duplicate custom field creation by detecting when
 * a field name already exists on the target platform as a custom field.
 *
 * @param sourceField - Source custom field to check
 * @param targetPlatformFields - Existing custom fields on target platform
 * @param targetPlatform - Target platform ("ap" | "cc")
 * @param requestId - Request ID for tracing
 * @returns Existing field if conflict found, null if field can be created
 */
function checkForExistingCustomFieldConflict(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatformFields: (APGetCustomFieldType | GetCCCustomField)[],
	targetPlatform: "ap" | "cc",
	requestId: string,
): APGetCustomFieldType | GetCCCustomField | null {
	const existingField = findExistingCustomField(
		sourceField.name,
		targetPlatformFields,
		targetPlatform,
	);

	if (existingField) {
		logInfo("Found existing custom field conflict", {
			requestId,
			sourceFieldId: sourceField.id,
			sourceFieldName: sourceField.name,
			existingFieldId: existingField.id,
			existingFieldName: existingField.name,
			targetPlatform,
			action: "creating_mapping_instead_of_duplicate",
		});
		return existingField;
	}

	return null;
}

/**
 * Comprehensive custom field synchronization function
 *
 * Fetches custom fields from both platforms, performs intelligent matching,
 * and maintains bidirectional field mappings in the local database.
 *
 * @param requestId - Request ID for tracing and logging
 * @returns Promise resolving to comprehensive synchronization results
 */
export async function synchronizeCustomFields(
	requestId: string,
): Promise<CustomFieldSyncResponse> {
	logInfo("Starting custom field synchronization", { requestId });

	const response: CustomFieldSyncResponse = {
		matchedCount: 0,
		upsertedCount: 0,
		unmatchedApFields: [],
		unmatchedCcFields: [],
		createdCcFields: [],
		createdApFields: [],
		blockedApFields: [],
		standardFieldMappings: [],
		statistics: {
			totalApFields: 0,
			totalCcFields: 0,
			totalProcessed: 0,
			totalMatched: 0,
			totalUnmatched: 0,
			totalStandardMappings: 0,
		},
		creationStatistics: {
			apFieldsCreatedInCc: 0,
			ccFieldsCreatedInAp: 0,
			totalCreated: 0,
			creationErrors: 0,
			creationSkippedDueToStandardFields: 0,
			creationBlockedCount: 0,
		},
		errors: [],
		creationErrors: [],
	};

	try {
		// Phase 1: Data Fetching
		logDebug("Fetching custom fields from both platforms", { requestId });

		const [apCustomFields, ccCustomFields, existingMappings] =
			await Promise.all([
				apiClient.ap.apCustomfield.all(),
				apiClient.cc.ccCustomfieldReq.all(),
				getDb().select().from(dbSchema.customFields),
			]);

		logInfo("Data fetching completed", {
			requestId,
			apFieldsCount: apCustomFields.length,
			ccFieldsCount: ccCustomFields.length,
			existingMappingsCount: existingMappings.length,
		});

		// Update statistics
		response.statistics.totalApFields = apCustomFields.length;
		response.statistics.totalCcFields = ccCustomFields.length;
		response.statistics.totalProcessed =
			apCustomFields.length + ccCustomFields.length;

		// Phase 2: Field Matching and Database Operations
		const processedCcFields = new Set<number>();
		const db = getDb();

		for (const apField of apCustomFields) {
			let matchFound = false;

			logDebug("Processing AP field", {
				requestId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				apFieldType: apField.dataType,
			});

			// Try to find a matching CC field
			for (const ccField of ccCustomFields) {
				if (processedCcFields.has(ccField.id)) {
					continue; // Skip already matched CC fields
				}

				if (fieldsMatch(apField, ccField)) {
					logDebug("Field match found", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						ccFieldLabel: ccField.label,
					});

					try {
						// Check if mapping already exists
						const existingMapping = existingMappings.find(
							(mapping) =>
								mapping.apId === apField.id || mapping.ccId === ccField.id,
						);

						const mappingData: CustomFieldInsert = {
							apId: apField.id,
							ccId: ccField.id,
							name: apField.name,
							label: ccField.label,
							type: apField.dataType,
							apConfig: apField,
							ccConfig: ccField,
							mappingType: "custom_to_custom",
							apStandardField: null,
							ccStandardField: null,
						};

						if (existingMapping) {
							// Update existing mapping
							await db
								.update(dbSchema.customFields)
								.set({
									...mappingData,
									updatedAt: new Date(),
								})
								.where(eq(dbSchema.customFields.id, existingMapping.id));

							logDebug("Updated existing field mapping", {
								requestId,
								mappingId: existingMapping.id,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						} else {
							// Insert new mapping with upsert logic
							await db
								.insert(dbSchema.customFields)
								.values(mappingData)
								.onConflictDoUpdate({
									target: [dbSchema.customFields.apId],
									set: {
										ccId: mappingData.ccId,
										name: mappingData.name,
										label: mappingData.label,
										type: mappingData.type,
										apConfig: mappingData.apConfig,
										ccConfig: mappingData.ccConfig,
										mappingType: mappingData.mappingType,
										apStandardField: mappingData.apStandardField,
										ccStandardField: mappingData.ccStandardField,
										updatedAt: new Date(),
									},
								});

							logDebug("Created new field mapping", {
								requestId,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						}

						response.upsertedCount++;
						processedCcFields.add(ccField.id);
						matchFound = true;
						break; // Move to next AP field
					} catch (error) {
						const errorMessage = `Failed to upsert field mapping: AP ${apField.id} -> CC ${ccField.id}`;
						logError(errorMessage, error);
						await logDbError(
							error instanceof Error ? error : new Error(String(error)),
							{
								type: "custom_field_sync",
								data: {
									apFieldId: apField.id,
									ccFieldId: ccField.id,
									operation: "upsert_mapping",
								},
							},
						);
						response.errors.push(errorMessage);
					}
				}
			}

			if (!matchFound) {
				response.unmatchedApFields.push(apField);
				logDebug("No match found for AP field", {
					requestId,
					apFieldId: apField.id,
					apFieldName: apField.name,
				});
			} else {
				response.matchedCount++;
			}
		}

		// Phase 3: Track unmatched CC fields
		for (const ccField of ccCustomFields) {
			if (!processedCcFields.has(ccField.id)) {
				response.unmatchedCcFields.push(ccField);
				logDebug("No match found for CC field", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					ccFieldLabel: ccField.label,
				});
			}
		}

		// Phase 4: Create unmatched AP fields in CC platform
		logInfo("Starting AP field creation in CC platform", {
			requestId,
			unmatchedApFieldsCount: response.unmatchedApFields.length,
		});

		for (const apField of response.unmatchedApFields) {
			try {
				// Check if this field maps to a CC standard field
				const standardMapping = checkForStandardFieldMapping(
					apField.name,
					"ap",
					"cc",
					requestId,
				);

				if (standardMapping) {
					// This field maps to a CC standard field, store the mapping instead of creating
					response.standardFieldMappings.push(standardMapping);
					response.creationStatistics.creationSkippedDueToStandardFields++;

					await storeStandardFieldMapping(apField, standardMapping, requestId);

					logInfo("Mapped AP custom field to CC standard field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccStandardField: standardMapping.targetField,
					});
					continue;
				}

				// Check if this field is blocked from CC creation
				const blocklistEntry = checkApToCcCreationBlocklist(apField.name);
				if (blocklistEntry) {
					// Field is blocked, add to blocked list and skip creation
					response.blockedApFields.push(apField);
					response.creationStatistics.creationBlockedCount++;

					logInfo("Blocked AP field from CC creation", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						blocklistPattern: blocklistEntry.pattern,
						blocklistReason: blocklistEntry.reason,
						isRegex: blocklistEntry.isRegex,
					});
					continue;
				}

				// Check if this field conflicts with existing CC custom fields
				const existingCcField = checkForExistingCustomFieldConflict(
					apField,
					ccCustomFields,
					"cc",
					requestId,
				);

				if (existingCcField) {
					// Found existing CC custom field with same name, create mapping instead of duplicate
					response.creationStatistics.creationSkippedDueToStandardFields++; // Using same counter for simplicity

					await storeMappingForCreatedFields(apField, existingCcField as GetCCCustomField, requestId);

					logInfo("Mapped AP custom field to existing CC custom field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						existingCcFieldId: existingCcField.id,
						existingCcFieldName: existingCcField.name,
					});
					continue;
				}

				// No conflicts, proceed with creation
				const createdCcField = await createApFieldInCc(apField, requestId);
				if (createdCcField) {
					response.createdCcFields.push(createdCcField);
					response.creationStatistics.apFieldsCreatedInCc++;

					// Store the mapping for the newly created field pair
					await storeMappingForCreatedFields(apField, createdCcField, requestId);

					logInfo("Successfully created and mapped CC field from AP field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccFieldId: createdCcField.id,
						ccFieldName: createdCcField.name,
					});
				} else {
					// Creation failed, try to find existing field as fallback
					const existingCcField = findExistingCustomField(apField.name, ccCustomFields, "cc");
					if (existingCcField) {
						logInfo("Found existing CC field after creation failure, creating mapping", {
							requestId,
							apFieldId: apField.id,
							apFieldName: apField.name,
							existingCcFieldId: existingCcField.id,
							existingCcFieldName: existingCcField.name,
						});

						await storeMappingForCreatedFields(apField, existingCcField as GetCCCustomField, requestId);
						response.creationStatistics.creationSkippedDueToStandardFields++;
					}
				}
			} catch (error) {
				const errorMessage = `Failed to create CC field from AP field ${apField.id}: ${apField.name}`;
				logError(errorMessage, error);
				await logDbError(
					error instanceof Error ? error : new Error(String(error)),
					{
						type: "custom_field_creation",
						data: {
							apFieldId: apField.id,
							apFieldName: apField.name,
							operation: "create_cc_from_ap",
						},
					},
				);
				response.creationErrors.push(errorMessage);
				response.creationStatistics.creationErrors++;
			}
		}

		// Phase 5: Create unmatched CC fields in AP platform
		logInfo("Starting CC field creation in AP platform", {
			requestId,
			unmatchedCcFieldsCount: response.unmatchedCcFields.length,
		});

		for (const ccField of response.unmatchedCcFields) {
			try {
				// Check if this field maps to an AP standard field
				const standardMapping = checkForStandardFieldMapping(
					ccField.name,
					"cc",
					"ap",
					requestId,
				);

				if (standardMapping) {
					// This field maps to an AP standard field, store the mapping instead of creating
					response.standardFieldMappings.push(standardMapping);
					response.creationStatistics.creationSkippedDueToStandardFields++;

					await storeStandardFieldMapping(ccField, standardMapping, requestId);

					logInfo("Mapped CC custom field to AP standard field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						apStandardField: standardMapping.targetField,
					});
					continue;
				}

				// Check if this field conflicts with existing AP custom fields
				const existingApField = checkForExistingCustomFieldConflict(
					ccField,
					apCustomFields,
					"ap",
					requestId,
				);

				if (existingApField) {
					// Found existing AP custom field with same name, create mapping instead of duplicate
					response.creationStatistics.creationSkippedDueToStandardFields++; // Using same counter for simplicity

					await storeMappingForCreatedFields(existingApField as APGetCustomFieldType, ccField, requestId);

					logInfo("Mapped CC custom field to existing AP custom field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						existingApFieldId: existingApField.id,
						existingApFieldName: existingApField.name,
					});
					continue;
				}

				// No conflicts, proceed with creation
				const createdApField = await createCcFieldInAp(ccField, requestId);
				if (createdApField) {
					response.createdApFields.push(createdApField);
					response.creationStatistics.ccFieldsCreatedInAp++;

					// Store the mapping for the newly created field pair
					await storeMappingForCreatedFields(createdApField, ccField, requestId);

					logInfo("Successfully created and mapped AP field from CC field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						apFieldId: createdApField.id,
						apFieldName: createdApField.name,
					});
				} else {
					// Creation failed, try to find existing field as fallback
					const existingApField = findExistingCustomField(ccField.name, apCustomFields, "ap");
					if (existingApField) {
						logInfo("Found existing AP field after creation failure, creating mapping", {
							requestId,
							ccFieldId: ccField.id,
							ccFieldName: ccField.name,
							existingApFieldId: existingApField.id,
							existingApFieldName: existingApField.name,
						});

						await storeMappingForCreatedFields(existingApField as APGetCustomFieldType, ccField, requestId);
						response.creationStatistics.creationSkippedDueToStandardFields++;
					}
				}
			} catch (error) {
				const errorMessage = `Failed to create AP field from CC field ${ccField.id}: ${ccField.name}`;
				logError(errorMessage, error);
				await logDbError(
					error instanceof Error ? error : new Error(String(error)),
					{
						type: "custom_field_creation",
						data: {
							ccFieldId: ccField.id,
							ccFieldName: ccField.name,
							operation: "create_ap_from_cc",
						},
					},
				);
				response.creationErrors.push(errorMessage);
				response.creationStatistics.creationErrors++;
			}
		}

		// Update creation statistics
		response.creationStatistics.totalCreated =
			response.creationStatistics.apFieldsCreatedInCc +
			response.creationStatistics.ccFieldsCreatedInAp;

		// Update final statistics
		response.statistics.totalMatched = response.matchedCount;
		response.statistics.totalUnmatched =
			response.unmatchedApFields.length + response.unmatchedCcFields.length;
		response.statistics.totalStandardMappings = response.standardFieldMappings.length;

		logInfo("Custom field synchronization completed", {
			requestId,
			matchedCount: response.matchedCount,
			upsertedCount: response.upsertedCount,
			unmatchedApCount: response.unmatchedApFields.length,
			unmatchedCcCount: response.unmatchedCcFields.length,
			createdCcFieldsCount: response.createdCcFields.length,
			createdApFieldsCount: response.createdApFields.length,
			blockedApFieldsCount: response.blockedApFields.length,
			standardFieldMappingsCount: response.standardFieldMappings.length,
			totalCreatedCount: response.creationStatistics.totalCreated,
			conflictsResolvedCount: response.creationStatistics.creationSkippedDueToStandardFields,
			creationBlockedCount: response.creationStatistics.creationBlockedCount,
			errorsCount: response.errors.length,
			creationErrorsCount: response.creationErrors.length,
			summary: "Includes standard field mappings, existing custom field conflict resolution, and creation blocklist filtering",
		});

		return response;
	} catch (error) {
		const errorMessage = "Custom field synchronization failed";
		logError(errorMessage, error);
		await logDbError(
			error instanceof Error ? error : new Error(String(error)),
			{
				type: "custom_field_sync",
				data: { operation: "synchronize_custom_fields" },
			},
		);
		response.errors.push(errorMessage);
		return response;
	}
}

/**
 * Handle Custom Fields webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function cfHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");

	try {
		const syncResult = await synchronizeCustomFields(requestId);

		return c.json(
			{
				message: "Custom field synchronization completed",
				requestId,
				timestamp: new Date().toISOString(),
				result: syncResult,
			},
			200,
		);
	} catch (error) {
		logError("Custom field handler failed", error);
		return c.json(
			{
				error: "Custom field synchronization failed",
				details: String(error),
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
