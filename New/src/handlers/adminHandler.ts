/**
 * <PERSON><PERSON>
 *
 * Handles administrative operations including error log cleanup.
 * Provides maintenance and monitoring functionality for the application.
 */

import { dbSchema, getDb } from "@database";
import { lt } from "drizzle-orm";
import type { Context } from "hono";
import { logError, logInfo } from "@/utils/logger";

/**
 * Handle error log cleanup operation
 *
 * Deletes error logs older than 90 days and returns deletion statistics.
 *
 * @param c - Hono context object
 * @returns JSON response with cleanup results
 */
export async function handleErrorCleanup(c: Context): Promise<Response> {
	const requestId = c.get("requestId");

	try {
		logInfo("Starting error log cleanup");

		const db = getDb();
		const ninetyDaysAgo = new Date();
		ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

		const deletedRecords = await db
			.delete(dbSchema.errorLogs)
			.where(lt(dbSchema.errorLogs.createdAt, ninetyDaysAgo))
			.returning({ id: dbSchema.errorLogs.id });

		logInfo(
			`Error log cleanup completed. Deleted ${deletedRecords.length} records`,
		);

		return c.json({
			message: "Error logs cleanup completed",
			deletedCount: deletedRecords.length,
			cutoffDate: ninetyDaysAgo.toISOString(),
			requestId,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logError("Error cleanup failed", error);
		return c.json(
			{
				error: "Failed to cleanup error logs",
				details: String(error),
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
